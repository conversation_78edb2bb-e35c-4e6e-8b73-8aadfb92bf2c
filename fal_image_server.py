"""
FastMCP Server for Image Generation using fal.ai
"""
import os
from typing import Optional, List, Dict, Any
from fastmcp import <PERSON>MC<PERSON>, Context
import fal_client

# Initialize FastMCP server
mcp = FastMCP("Fal.ai Image Generator 🎨")

# Configure fal client if API key is set
api_key = os.getenv("FAL_KEY")
if api_key:
    fal_client.config(credentials=api_key)


@mcp.tool
async def generate_image(
    prompt: str,
    model: str = "flux-dev",
    num_images: int = 1,
    image_size: str = "landscape_4_3",
    seed: Optional[int] = None,
    num_inference_steps: Optional[int] = None,
    guidance_scale: Optional[float] = None,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    Generate images using fal.ai models.
    
    Args:
        prompt: Text description of the image to generate
        model: Model to use ('flux-dev', 'flux-schnell', 'flux-pro')
        num_images: Number of images to generate (1-4)
        image_size: Image size ('square', 'landscape_4_3', 'portrait_4_3', etc.)
        seed: Random seed for reproducible results
        num_inference_steps: Quality/speed tradeoff (higher = better quality, slower)
        guidance_scale: How closely to follow the prompt (default: 3.5)
        ctx: MCP context for logging
    
    Returns:
        Dictionary containing generated image URLs and metadata
    """
    
    # Log the request
    if ctx:
        await ctx.info(f"Generating {num_images} image(s) with prompt: {prompt[:100]}...")
    
    # Map model names to fal.ai endpoints
    model_endpoints = {
        "flux-dev": "fal-ai/flux/dev",
        "flux-schnell": "fal-ai/flux/schnell",
        "flux-pro": "fal-ai/flux/pro"
    }
    
    endpoint = model_endpoints.get(model, "fal-ai/flux/dev")
    
    # Build arguments
    arguments = {
        "prompt": prompt,
        "num_images": min(max(num_images, 1), 4),  # Clamp between 1-4
        "image_size": image_size
    }
    
    if seed is not None:
        arguments["seed"] = seed
    
    if num_inference_steps is not None:
        arguments["num_inference_steps"] = num_inference_steps
    
    if guidance_scale is not None:
        arguments["guidance_scale"] = guidance_scale
    
    try:
        # Generate images
        result = await fal_client.subscribe_async(
            endpoint,
            arguments=arguments,
            on_queue_update=lambda update: None  # Silent progress updates
        )
        
        # Extract image URLs from result
        images = result.get("images", [])
        
        if ctx:
            await ctx.info(f"Successfully generated {len(images)} image(s)")
        
        return {
            "success": True,
            "images": images,
            "model": model,
            "prompt": prompt,
            "seed": arguments.get("seed"),
            "metadata": {
                "num_images": len(images),
                "image_size": image_size,
                "endpoint": endpoint
            }
        }
        
    except Exception as e:
        error_msg = f"Error generating images: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        
        return {
            "success": False,
            "error": error_msg,
            "model": model,
            "prompt": prompt
        }


@mcp.tool
async def generate_image_from_image(
    image_url: str,
    prompt: str,
    strength: float = 0.85,
    model: str = "flux-dev",
    seed: Optional[int] = None,
    num_inference_steps: int = 40,
    ctx: Context = None
) -> Dict[str, Any]:
    """
    Transform an existing image using fal.ai image-to-image models.
    
    Args:
        image_url: URL of the source image
        prompt: Text description of the transformation
        strength: Transformation strength (0.0-1.0, higher = more transformation)
        model: Model to use (currently only 'flux-dev' supports image-to-image)
        seed: Random seed for reproducible results
        num_inference_steps: Quality/speed tradeoff (higher = better quality, slower)
        ctx: MCP context for logging
    
    Returns:
        Dictionary containing transformed image URL and metadata
    """
    
    if ctx:
        await ctx.info(f"Transforming image with prompt: {prompt[:100]}...")
    
    # Currently only flux-dev supports image-to-image
    endpoint = "fal-ai/flux/dev/image-to-image"
    
    arguments = {
        "image_url": image_url,
        "prompt": prompt,
        "strength": max(0.0, min(1.0, strength)),  # Clamp between 0-1
        "num_inference_steps": num_inference_steps
    }
    
    if seed is not None:
        arguments["seed"] = seed
    
    try:
        # Transform image
        result = await fal_client.subscribe_async(
            endpoint,
            arguments=arguments,
            on_queue_update=lambda update: None  # Silent progress updates
        )
        
        # Extract image URL from result
        image = result.get("images", [{}])[0] if result.get("images") else {}
        
        if ctx:
            await ctx.info("Successfully transformed image")
        
        return {
            "success": True,
            "image": image,
            "source_image": image_url,
            "prompt": prompt,
            "strength": strength,
            "seed": arguments.get("seed"),
            "metadata": {
                "endpoint": endpoint,
                "num_inference_steps": num_inference_steps
            }
        }
        
    except Exception as e:
        error_msg = f"Error transforming image: {str(e)}"
        if ctx:
            await ctx.error(error_msg)
        
        return {
            "success": False,
            "error": error_msg,
            "source_image": image_url,
            "prompt": prompt
        }


@mcp.tool
def list_available_models() -> List[Dict[str, str]]:
    """
    List all available fal.ai image generation models and their descriptions.
    
    Returns:
        List of available models with their descriptions
    """
    return [
        {
            "name": "flux-dev",
            "description": "FLUX.1 [dev] - 12B parameter model for high-quality images",
            "inference_steps": "28 (default)",
            "use_case": "General purpose, high quality"
        },
        {
            "name": "flux-schnell",
            "description": "FLUX.1 [schnell] - Fast generation in 1-4 steps",
            "inference_steps": "4 (default)",
            "use_case": "Quick generation, good quality"
        },
        {
            "name": "flux-pro",
            "description": "FLUX.1 [pro] - Professional-grade highest quality",
            "inference_steps": "Variable",
            "use_case": "Professional use, best quality"
        }
    ]


@mcp.tool
def get_image_size_options() -> List[Dict[str, str]]:
    """
    Get all available image size options for generation.
    
    Returns:
        List of available image sizes with their descriptions
    """
    return [
        {"size": "square", "description": "1:1 aspect ratio"},
        {"size": "square_hd", "description": "1:1 aspect ratio, high definition"},
        {"size": "landscape_4_3", "description": "4:3 landscape orientation"},
        {"size": "landscape_16_9", "description": "16:9 landscape orientation"},
        {"size": "portrait_4_3", "description": "3:4 portrait orientation"},
        {"size": "portrait_16_9", "description": "9:16 portrait orientation"},
    ]


@mcp.prompt
def image_generation_prompt(
    subject: str,
    style: Optional[str] = None,
    mood: Optional[str] = None,
    details: Optional[str] = None
) -> str:
    """
    Generate a well-formatted prompt for image generation.
    
    Args:
        subject: Main subject of the image
        style: Art style (e.g., "oil painting", "digital art", "photograph")
        mood: Mood or atmosphere (e.g., "serene", "dramatic", "whimsical")
        details: Additional details to include
    
    Returns:
        A well-formatted prompt string
    """
    parts = [subject]
    
    if style:
        parts.append(f"in the style of {style}")
    
    if mood:
        parts.append(f"with a {mood} mood")
    
    if details:
        parts.append(details)
    
    return ", ".join(parts)


if __name__ == "__main__":
    # Check for API key
    if not api_key:
        print("⚠️  Warning: FAL_KEY environment variable not set")
        print("   Set it with: export FAL_KEY='your-api-key'")
        print("   Get your API key from: https://fal.ai/dashboard/keys")
        print()
    
    print("🚀 Starting Fal.ai Image Generation MCP Server...")
    print("   Available tools:")
    print("   - generate_image: Generate images from text prompts")
    print("   - generate_image_from_image: Transform existing images")
    print("   - list_available_models: See all available models")
    print("   - get_image_size_options: See all image size options")
    print()
    
    # Run the server
    mcp.run()